# 修复规划合理性BUG与架构升级计划

## 1. 问题诊断 (Problem Diagnosis)

### 1.1. 核心问题
当前的行程规划逻辑存在根本性缺陷，其本质是**“粗放式POI分组”**，而非一个真正符合逻辑、可执行的**“时序化行程规划”**。Agent不懂得在时间和空间维度上进行思考。

### 1.2. 具体症状
- **无时间概念**: 规划结果中没有具体的时间点（如`09:00`, `12:30`），无法区分上午、中午、下午的活动适宜性。
- **无空间概念**: 无法计算POI之间的距离和交通时间，可能导致路线规划不合理，例如在城市两端反复横跳。
- **无类型化调度**: 未能根据POI的类型（景点、餐厅、酒店）进行逻辑调度，例如将餐厅安排在不合适的时段。
- **住宿策略混乱**: 未将酒店作为每日行程的“基点”，导致行程起点不明确，或可能每日更换酒店。
- **产出僵化**: 倾向于为每天分配固定数量（如3个）的POI，缺乏灵活性和真实性。

### 1.3. 关联BUG与统一修复
此架构升级将**根治**最初发现的“两天行程POI重复”问题。重复的根本原因在于旧架构没有一个动态的、被消耗的“POI池”概念。新架构的“每日时序规划器”通过维护一个`poi_pool`状态并从中动态规划，自然地解决了这个问题。**本文档将作为修复该BUG的唯一指导方案。**

## 2. 解决方案：引入“每日时序规划器” (Daily Sequential Planner)

我们将对规划逻辑进行根本性的重构，将Agent的思考模式从规划“一整天”转变为规划“**一天中的下一个活动**”。

### 2.1. 核心逻辑
1.  **住宿先行 (Accommodation First)**: 在规划任何一天的活动前，先确定核心住宿点。对于单城市旅行，将只选择一个酒店作为所有天数的“大本营”。
2.  **每日时序规划循环 (Daily Sequential Planning Loop)**: 针对每一天，启动一个基于时间的内部循环。
    - **循环状态**: 维护 `current_day`, `current_time`, `current_location` 三个关键状态变量。
    - **循环迭代**: 循环的每一步都旨在规划下一个活动，直到一天的结束时间（如21:00）。
    - **循环终止**: 当 `current_time` 超出预设的每日活动结束时间时，结束当天的规划。

### 2.2. 单步迭代流程 (Think -> Decide -> Act -> Schedule)
在每日循环的每一步中，Agent将执行以下操作：
1.  **思考 (Think)**: LLM根据 `(current_day, current_time, current_location)` 和用户偏好，思考下一步最适合的**活动类型**（如“景点”或“午餐”）和**预估时长**。
2.  **决策 (Decide)**: LLM将思考结果固化为一个明确的行动目标。
3.  **行动 (Act)**: Agent调用工具（如 `search_nearby_poi`, `get_driving_time`）来寻找具体的POI并计算交通时间。
4.  **调度 (Schedule)**: Agent将包含明确起止时间的活动添加至当天的计划中，并更新 `current_time` 和 `current_location`，为下一次迭代做准备。

## 3. 文件修改清单 (Modification Checklist)

### 3.1. 核心逻辑层
- **文件位置**: `src/agents/travel_planner_lg/nodes.py`
- **修改内容**:
    1.  **重构 `run_icp_planning` 函数**:
        - 废弃当前宏观的迭代循环。
        - 实现新的两级循环结构：外循环遍历每一天，内循环基于时间规划当天的每一个活动。
        - 在函数开始处，实现“住宿先行”逻辑，确定基点酒店。
        - 在内循环中，严格管理和传递 `current_time` 和 `current_location` 状态。
    2.  **调整对 `think_tool` 的调用**:
        - 修改传递给 `think_tool` 的参数，必须包含新的上下文状态。

### 3.2. 工具层
- **文件位置**: `src/tools/travel_planner/icp_tools.py` (或其他ICP工具定义文件)
- **修改内容**:
    1.  **修改 `generate_planning_thought` 工具**:
        - 改造其内部Prompt，使其能够理解并处理包含 `current_time` 和 `current_location` 的新输入，以进行小颗粒度的“下一步活动”决策。
    2.  **修改 `update_planning_state` 工具**:
        - 增强该工具，使其能够处理带有详细时间戳的活动，并正确更新 `icp_planner_state` 中的 `current_time` 和 `current_location`。
    3.  **新增/增强工具 (细化)**:
        - **`search_nearby_poi(current_location: Dict[str, float], poi_type: str, radius_meters: int = 5000, excluded_pois: List[str] = []) -> List[Dict]`**:
          - **功能**: 在指定地点附近搜索特定类型的POI。
          - **参数**: `current_location` (包含`lat`, `lon`的字典), `poi_type` (如 'restaurant', 'attraction'), `radius_meters` (搜索半径), `excluded_pois` (需要排除的POI ID列表，防止重复规划)。
          - **返回**: 找到的POI列表。
        - **`get_driving_time(origin: Dict[str, float], destination: Dict[str, float]) -> int`**:
          - **功能**: 获取两点间的预计驾驶时间。
          - **参数**: 起点和终点的坐标字典。
          - **返回**: 以分钟为单位的交通时间。
        - **数据丰富化工具 (Data Enrichment)**:
          - **`get_poi_details(poi_id: str) -> Dict`**: 获取POI的详细信息，如评分、评论摘要、开放时间等。此步骤应在“行动(Act)”阶段被调用，以丰富规划内容。

### 3.3. Prompt层
- **文件位置**: `src/prompts/travel_planner/`
- **修改内容**:
    - **废弃旧Prompt**: `04_itinerary_generator.md` 文件是为旧的、一次性生成模式设计的，在新架构下应被废弃。
    - **创建新Prompt**: 需要创建一个全新的Prompt文件，例如 `05_icp_step_planner.md`。这个Prompt是新架构的“引擎”，将专门用于`planner_agent_node`（或`think_tool`）中，其核心指令将是：
      > "你是行程规划专家。这是你的任务上下文：{current_state_json}。基于此，请决定下一步最合理的行动和预估耗时，并以指定的JSON格式返回你的思考和行动指令。"
    - **Prompt上下文**: 该Prompt必须能够接收并理解包含`current_day`, `current_time`, `current_location`, `remaining_pois`, `user_preferences`等动态信息的复杂上下文。
    - **管理与注入方式**: 所有提示词模板都将统一存放在 `src/prompts/travel_planner/` 目录下。它们将通过现有的加载机制（例如，通过 `prompts.loader` 或作为 `Planner Tool` 的一部分）按需加载和格式化，注入到Agent的思考流程中。

### 3.4. 状态模型层 (State Model)
- **文件位置**: `src/agents/travel_planner_lg/state.py`
- **修改内容**: 需要在 `TravelPlannerState` TypedDict 中添加或明确以下字段来支持新的规划流程：
    ```python
    from typing import TypedDict, Optional, List, Dict, Any

    class TravelPlannerState(TypedDict):
        # ... a lot of existing fields ...
        
        # 新增或明确的字段
        
        # 规划器内部状态，用于驱动时序循环
        icp_planner_state: Optional[Dict[str, Any]]
        
        # 存储所有待规划的、经过初步筛选的POI
        poi_pool: List[Dict]

        # 最终生成的结构化行程
        # 格式: {1: [{"type": "hotel", ...}, {"type": "attraction", ...}], 2: [...]}
        structured_itinerary: Dict[int, List[Dict]]
    ```
- **`icp_planner_state` 字典结构**:
  ```json
  {
    "current_day": 1,
    "current_time": "09:00",
    "current_location": {"name": "酒店名称", "lat": 39.9, "lon": 116.4},
    "is_done": false
  }
  ```
- **职责**: 这些状态字段将成为整个时序规划流程的数据主线，由 `update_planning_state` 工具进行统一、安全地更新。

## 4. 风险评估与缓解策略 (Risks & Mitigations)

- **风险 1: LLM调用次数增加导致延迟和成本上升。**
  - **描述**: 新模式下，每个活动都需要一次LLM调用，相比之前每天一次，调用频率显著增加。
  - **缓解策略**: 对负责单步思考的 `think_tool` 使用更轻量、更快速的模型（如配置中的 `basic`），因为它处理的是相对简单的决策。将更强大的 `reasoning` 保留用于初始的复杂意图分析阶段。

- **风险 2: 规划逻辑可能陷入死循环或失败。**
  - **描述**: 如果在某个时间点找不到合适的POI，或者时间计算出错，Agent可能会卡住。
  - **缓解策略**:
    1.  **增加失败重试计数器**: 如果连续2-3次无法为某个时间段安排活动，则跳过该时段，标记为“自由活动”。
    2.  **设置硬性终止条件**: 为每一天的规划循环设置最大迭代次数，防止无限循环。
    3.  **增强日志**: 详细记录每一次状态更新（`current_time`, `current_location`的变化），便于快速定位问题。

- **风险 3: 状态管理复杂度增加。**
  - **描述**: 实时追踪 `current_time` 和 `current_location` 比之前的状态管理更复杂，容易出错。
  - **缓解策略**: 在 `update_planning_state` 工具中进行集中的、原子化的状态更新，并编写单元测试来验证状态转换的正确性。

## 5. 分层模型策略 (Layered Model Strategy)

为了在保证规划质量的同时，优化系统响应速度和运行成本，我们采用分层式的LLM调用策略，针对不同阶段的任务使用不同能力的模型。

| 模型角色 | 职责定位 | 何时使用 | 建议模型 (配置名) | 核心原因 |
| :--- | :--- | :--- | :--- | :--- |
| **常规/强大模型** | **战略规划师** | **规划开始时**，进行一次性的宏观意图分析。 | `reasoning` | 在战略层面需要最准确的意图理解，为整个行程定下正确的基调，一次性的高质量投入是完全值得的。 |
| **Think/轻量模型** | **战术执行官** | **规划循环中**，进行高频次的、小颗粒度的“下一步”思考。 | `basic` | 战术决策的上下文约束强、任务相对简单，使用轻量模型可以极大降低延迟和成本，保证规划的实时性和流畅性。 |

### 代码实现指南

- **宏观意图分析**: 在 `run_framework_analysis` 和 `run_preference_analysis` 节点中，应实例化`ReasoningService`并明确指定使用强大模型。
  ```python
  # in nodes.py -> run_framework_analysis
  reasoning_service = ReasoningService(llm_role="reasoning")
  ```

- **ICP循环内思考**: 在 `run_icp_planning` 函数内部，当调用负责“思考下一步”的 `think_tool` 时，应明确指定使用轻量模型。
  ```python
  # in nodes.py -> run_icp_planning (伪代码)
  # ... 循环内部 ...
  think_service = ReasoningService(llm_role="basic")
  thought_result = await think_service.chat(...) # 调用轻量模型进行思考
  ```
