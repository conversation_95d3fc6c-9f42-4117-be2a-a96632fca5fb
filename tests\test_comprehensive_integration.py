#!/usr/bin/env python3
"""
旅行规划系统重构后的综合集成测试

阶段1：前端集成测试
阶段2：端到端工作流测试

严格使用真实数据，禁止模拟或虚假数据
遵循doc/重构/目录下的重构文档规范
"""

import asyncio
import logging
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, Any, List
from playwright.async_api import async_playwright, Page, Browser

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'comprehensive_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

class ComprehensiveIntegrationTest:
    def __init__(self):
        self.browser = None
        self.page = None
        self.base_url = "http://localhost:8000"
        self.test_results = {}
        
    async def setup_browser(self):
        """设置浏览器环境"""
        logger.info("=== 设置浏览器环境 ===")
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口以便观察
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        self.page = await context.new_page()
        
        # 启用控制台日志监听
        self.page.on("console", lambda msg: logger.info(f"Browser Console: {msg.text}"))
        self.page.on("pageerror", lambda error: logger.error(f"Browser Error: {error}"))
        
        logger.info("浏览器环境设置完成")
        
    async def test_frontend_integration(self) -> bool:
        """阶段1：前端集成测试"""
        logger.info("=== 阶段1：前端集成测试 ===")
        
        try:
            # 1. 打开前端页面
            logger.info("1. 打开前端页面")
            await self.page.goto(f"{self.base_url}/static/index.html")
            await self.page.wait_for_load_state('networkidle')
            
            # 验证页面加载
            title = await self.page.title()
            logger.info(f"页面标题: {title}")
            assert "AutoPilot AI" in title, "页面标题不正确"
            
            # 2. 验证用户输入表单
            logger.info("2. 验证用户输入表单")
            
            # 检查查询输入框
            query_input = self.page.locator("#userQuery")
            await query_input.wait_for(state="visible")
            
            # 输入测试查询
            test_query = "我想去北京旅游3天，喜欢历史文化景点，预算3000元"
            await query_input.fill(test_query)
            logger.info(f"输入测试查询: {test_query}")
            
            # 检查车辆信息面板并选择运行模式
            toggle_vehicle_btn = self.page.locator("#toggleVehicleInfo")
            await toggle_vehicle_btn.click()
            logger.info("展开车辆信息面板")

            # 等待面板展开
            await self.page.wait_for_timeout(1000)

            # 选择运行模式
            mode_select = self.page.locator("#drivingMode")
            await mode_select.wait_for(state="visible")
            await mode_select.select_option("automatic")
            logger.info("选择全自动运行模式")
            
            # 3. 提交表单并验证V3 API调用
            logger.info("3. 提交表单并验证V3 API调用")
            
            # 监听网络请求
            api_requests = []
            self.page.on("request", lambda request: api_requests.append(request))
            
            # 点击开始规划按钮
            start_button = self.page.locator("#planButton")
            await start_button.click()
            logger.info("点击开始智能分析按钮")
            
            # 等待API请求
            await self.page.wait_for_timeout(2000)
            
            # 验证V3 API请求
            v3_requests = [req for req in api_requests if "/api/v3/travel-planner/plan" in req.url]
            assert len(v3_requests) > 0, "未发现V3 API请求"
            logger.info(f"发现 {len(v3_requests)} 个V3 API请求")
            
            # 4. 验证SSE流式响应显示
            logger.info("4. 验证SSE流式响应显示")
            
            # 等待分析结果显示
            await self.page.wait_for_timeout(5000)
            
            # 检查分析结果区域
            analysis_section = self.page.locator("#analysisSteps")
            await analysis_section.wait_for(state="visible", timeout=10000)
            logger.info("分析结果区域已显示")
            
            # 5. 验证意图分析结果展示
            logger.info("5. 验证意图分析结果展示")
            
            # 检查各个分析阶段
            phases_to_check = ["A.1", "A.2", "A.3", "A.4", "A.5"]
            for phase in phases_to_check:
                phase_element = self.page.locator(f"[data-phase='{phase}']")
                if await phase_element.count() > 0:
                    logger.info(f"发现分析阶段 {phase}")
                else:
                    logger.warning(f"未发现分析阶段 {phase}")
            
            # 6. 验证"立即规划"按钮出现
            logger.info("6. 验证'立即规划'按钮出现")
            
            # 等待立即规划按钮出现
            immediate_planning_button = self.page.locator("#startPlanningBtn")
            try:
                await immediate_planning_button.wait_for(state="visible", timeout=15000)
                logger.info("'立即开始规划'按钮已出现")

                # 点击立即规划按钮
                await immediate_planning_button.click()
                logger.info("点击'立即开始规划'按钮")

            except Exception as e:
                logger.warning(f"'立即开始规划'按钮未出现或点击失败: {e}")
            
            # 7. 验证V3架构替代legacy路由
            logger.info("7. 验证V3架构替代legacy路由")
            
            # 检查是否有legacy路由请求
            legacy_requests = [req for req in api_requests if "/api/travel/plan" in req.url and "/api/v3/" not in req.url]
            if len(legacy_requests) == 0:
                logger.info("[PASS] 成功使用V3架构，未发现legacy路由请求")
            else:
                logger.warning(f"[WARN] 发现 {len(legacy_requests)} 个legacy路由请求")

            logger.info("[PASS] 前端集成测试完成")
            return True

        except Exception as e:
            logger.error(f"[FAIL] 前端集成测试失败: {str(e)}")
            return False
    
    async def test_end_to_end_workflow(self) -> bool:
        """阶段2：端到端工作流测试"""
        logger.info("=== 阶段2：端到端工作流测试 ===")
        
        try:
            # 1. 测试Stage A：意图分析
            logger.info("1. 测试Stage A：意图分析")
            
            # 等待分析完成
            await self.page.wait_for_timeout(10000)
            
            # 检查分析阶段完成状态
            analysis_phases = ["A.1", "A.2", "A.3", "A.4", "A.5"]
            completed_phases = 0
            
            for phase in analysis_phases:
                phase_status = self.page.locator(f"[data-phase='{phase}'] .phase-status")
                if await phase_status.count() > 0:
                    status_text = await phase_status.text_content()
                    if "完成" in status_text or "✅" in status_text:
                        completed_phases += 1
                        logger.info(f"阶段 {phase} 已完成")
            
            logger.info(f"完成的分析阶段: {completed_phases}/{len(analysis_phases)}")
            
            # 2. 测试Stage B：ICP迭代规划
            logger.info("2. 测试Stage B：ICP迭代规划")
            
            # 等待规划阶段开始
            await self.page.wait_for_timeout(15000)
            
            # 检查规划结果
            planning_results = self.page.locator("#planningResults")
            if await planning_results.count() > 0:
                logger.info("规划结果区域已显示")
                
                # 检查POI信息
                poi_elements = self.page.locator(".poi-item")
                poi_count = await poi_elements.count()
                logger.info(f"发现 {poi_count} 个POI项目")
                
                # 验证餐厅详细信息
                restaurant_elements = self.page.locator(".poi-item[data-type='restaurant']")
                restaurant_count = await restaurant_elements.count()
                logger.info(f"发现 {restaurant_count} 个餐厅")
                
                # 检查餐厅详细信息
                for i in range(min(restaurant_count, 3)):  # 检查前3个餐厅
                    restaurant = restaurant_elements.nth(i)
                    name_element = restaurant.locator(".poi-name")
                    address_element = restaurant.locator(".poi-address")
                    
                    if await name_element.count() > 0 and await address_element.count() > 0:
                        name = await name_element.text_content()
                        address = await address_element.text_content()
                        logger.info(f"餐厅 {i+1}: {name} - {address}")
            
            # 3. 验证关键功能点
            logger.info("3. 验证关键功能点")
            
            # 检查控制台日志中的POI搜索和重试信息
            await self.page.wait_for_timeout(5000)
            
            logger.info("[PASS] 端到端工作流测试完成")
            return True

        except Exception as e:
            logger.error(f"[FAIL] 端到端工作流测试失败: {str(e)}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
            logger.info("浏览器已关闭")
    
    async def run_comprehensive_test(self) -> bool:
        """运行综合测试"""
        logger.info("开始旅行规划系统重构后的综合集成测试")
        
        try:
            await self.setup_browser()
            
            # 阶段1：前端集成测试
            frontend_result = await self.test_frontend_integration()
            self.test_results["frontend_integration"] = frontend_result
            
            # 阶段2：端到端工作流测试
            e2e_result = await self.test_end_to_end_workflow()
            self.test_results["end_to_end_workflow"] = e2e_result
            
            # 汇总测试结果
            logger.info("\n=== 综合测试结果汇总 ===")
            passed_tests = 0
            total_tests = len(self.test_results)
            
            for test_name, result in self.test_results.items():
                status = "[PASS]" if result else "[FAIL]"
                logger.info(f"{test_name}: {status}")
                if result:
                    passed_tests += 1

            logger.info(f"\n总计: {passed_tests}/{total_tests} 测试通过")

            if passed_tests == total_tests:
                logger.info("[SUCCESS] 所有综合测试通过！系统重构验证成功！")
                return True
            else:
                logger.warning("[WARN] 部分测试失败，需要进一步修复")
                return False
                
        except Exception as e:
            logger.error(f"综合测试执行失败: {str(e)}")
            return False
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    test_runner = ComprehensiveIntegrationTest()
    success = await test_runner.run_comprehensive_test()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
