#!/usr/bin/env python3
"""
简化的前端集成测试
专门验证前端与V3 API的集成功能
"""

import asyncio
import logging
import sys
import os
import time
from datetime import datetime
from playwright.async_api import async_playwright

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

async def test_frontend_integration():
    """简化的前端集成测试"""
    logger.info("=== 开始前端集成测试 ===")
    
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    context = await browser.new_context(viewport={'width': 1920, 'height': 1080})
    page = await context.new_page()
    
    # 启用控制台日志监听
    page.on("console", lambda msg: logger.info(f"Browser: {msg.text}"))
    page.on("pageerror", lambda error: logger.error(f"Browser Error: {error}"))
    
    try:
        # 1. 打开前端页面
        logger.info("1. 打开前端页面")
        await page.goto("http://localhost:8000/static/index.html")
        await page.wait_for_load_state('networkidle')
        
        title = await page.title()
        logger.info(f"页面标题: {title}")
        
        # 2. 填写表单
        logger.info("2. 填写测试表单")
        query_input = page.locator("#userQuery")
        await query_input.fill("我想去北京旅游3天，喜欢历史文化景点")
        
        # 展开车辆信息面板
        toggle_btn = page.locator("#toggleVehicleInfo")
        await toggle_btn.click()
        await page.wait_for_timeout(1000)
        
        # 选择运行模式
        mode_select = page.locator("#drivingMode")
        await mode_select.select_option("automatic")
        logger.info("表单填写完成")
        
        # 3. 监听网络请求
        api_requests = []
        page.on("request", lambda request: api_requests.append(request))
        
        # 4. 提交表单
        logger.info("3. 提交表单")
        submit_btn = page.locator("#planButton")
        await submit_btn.click()
        
        # 5. 等待API响应
        logger.info("4. 等待API响应")
        await page.wait_for_timeout(5000)
        
        # 6. 验证V3 API调用
        v3_requests = [req for req in api_requests if "/api/v3/travel-planner/plan" in req.url]
        logger.info(f"发现 {len(v3_requests)} 个V3 API请求")
        
        # 7. 检查分析步骤容器
        analysis_steps = page.locator("#analysisSteps")
        if await analysis_steps.is_visible():
            logger.info("分析步骤容器已显示")
            
            # 检查是否有分析步骤内容
            step_elements = page.locator("#analysisSteps .analysis-step")
            step_count = await step_elements.count()
            logger.info(f"发现 {step_count} 个分析步骤")
        
        # 8. 等待并检查立即规划按钮
        logger.info("5. 检查立即规划按钮")
        planning_btn = page.locator("#startPlanningBtn")
        
        # 等待按钮出现（最多等待20秒）
        for i in range(20):
            if await planning_btn.is_visible():
                logger.info("立即规划按钮已出现")
                break
            await page.wait_for_timeout(1000)
        else:
            logger.warning("立即规划按钮未在20秒内出现")
        
        # 9. 等待规划完成
        logger.info("6. 等待规划完成")
        await page.wait_for_timeout(10000)
        
        # 10. 检查规划结果
        results_panel = page.locator("#resultsPanel")
        if await results_panel.is_visible():
            logger.info("规划结果面板已显示")
            
            # 检查POI项目
            poi_items = page.locator(".poi-item")
            poi_count = await poi_items.count()
            logger.info(f"发现 {poi_count} 个POI项目")
            
            # 检查餐厅信息
            restaurants = page.locator(".poi-item[data-type='restaurant']")
            restaurant_count = await restaurants.count()
            logger.info(f"发现 {restaurant_count} 个餐厅")
        
        # 11. 验证无legacy路由请求
        legacy_requests = [req for req in api_requests if "/api/travel/plan" in req.url and "/api/v3/" not in req.url]
        if len(legacy_requests) == 0:
            logger.info("[PASS] 成功使用V3架构，未发现legacy路由请求")
        else:
            logger.warning(f"[WARN] 发现 {len(legacy_requests)} 个legacy路由请求")
        
        logger.info("[SUCCESS] 前端集成测试完成")
        return True
        
    except Exception as e:
        logger.error(f"[FAIL] 前端集成测试失败: {str(e)}")
        return False
    finally:
        await browser.close()

async def main():
    """主函数"""
    success = await test_frontend_integration()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
