#!/usr/bin/env python3
"""
旅行规划系统重构后的快速验证脚本

测试内容：
1. 验证新的每日时序规划器
2. 测试POI去重和距离逻辑
3. 验证时空一致性
4. 测试真实数据调用

严禁使用模拟数据，必须使用真实数据进行测试
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'test_refactored_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

async def test_poi_search_retry():
    """测试POI搜索重试逻辑"""
    logger.info("=== 测试POI搜索重试逻辑 ===")
    
    try:
        from src.tools.travel_planner.amap_poi_tools import search_poi
        
        # 测试正常搜索
        results = await search_poi(
            keywords="故宫",
            city="北京",
            limit=5
        )
        
        logger.info(f"POI搜索成功，返回{len(results)}个结果")
        for i, poi in enumerate(results[:3]):
            logger.info(f"  {i+1}. {poi.get('name', 'Unknown')} - {poi.get('address', 'No address')}")
        
        return True
        
    except Exception as e:
        logger.error(f"POI搜索测试失败: {str(e)}")
        return False

async def test_icp_tools():
    """测试ICP工具的时空感知能力"""
    logger.info("=== 测试ICP工具时空感知能力 ===")
    
    try:
        from src.tools.travel_planner.icp_tools import (
            generate_planning_thought,
            _select_balanced_pois
        )
        from src.agents.travel_planner_lg.nodes import (
            _advance_time,
            _should_plan_meal
        )
        
        # 测试时空状态管理
        current_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "12:00",
                "current_location": {
                    "name": "故宫博物院",
                    "lat": 39.917,
                    "lon": 116.397
                }
            },
            "daily_plans": {
                1: [
                    {
                        "name": "故宫博物院",
                        "poi_type": "ATTRACTION",
                        "location": "116.397,39.917"
                    }
                ]
            },
            "consolidated_intent": {
                "travel_days": 3,
                "destinations": ["北京"]
            },
            "total_budget_tracker": 200
        }
        
        planning_context = {
            "constraints": {
                "budget_limit": 1000
            }
        }
        
        # 测试思考工具
        thought_result = generate_planning_thought(current_state, planning_context, 2)
        logger.info(f"思考结果: {thought_result.get('thought_content', 'No content')}")
        logger.info(f"时间上下文: {thought_result.get('current_time_context', {})}")
        
        # 测试时间推进
        new_time = _advance_time("12:00", 90)
        logger.info(f"时间推进: 12:00 + 90分钟 = {new_time}")
        
        # 测试用餐时间检查
        need_meal = _should_plan_meal("12:30", current_state["daily_plans"][1])
        logger.info(f"12:30是否需要用餐: {need_meal}")
        
        return True
        
    except Exception as e:
        logger.error(f"ICP工具测试失败: {str(e)}")
        return False

async def test_poi_deduplication():
    """测试POI去重逻辑"""
    logger.info("=== 测试POI去重逻辑 ===")
    
    try:
        from src.tools.travel_planner.icp_tools import _select_balanced_pois
        
        # 模拟POI池
        remaining_pois = [
            {
                "id": "B000A8UIN8",
                "name": "故宫博物院",
                "typecode": "110101",
                "type": "风景名胜",
                "location": "116.397,39.917",
                "rating": "4.9"
            },
            {
                "id": "B000A7BD6C", 
                "name": "天安门广场",
                "typecode": "110101",
                "type": "风景名胜",
                "location": "116.391,39.903",
                "rating": "4.8"
            },
            {
                "id": "B000A83M3R",
                "name": "全聚德烤鸭店",
                "typecode": "050100",
                "type": "餐饮服务",
                "location": "116.398,39.918",
                "rating": "4.5"
            }
        ]
        
        existing_poi_types = {"ATTRACTION": 1}
        current_location = {"lat": 39.917, "lon": 116.397}
        
        # 测试智能选择
        selected_pois = _select_balanced_pois(remaining_pois, existing_poi_types, 3, current_location)
        
        logger.info(f"智能选择了{len(selected_pois)}个POI:")
        for poi in selected_pois:
            logger.info(f"  - {poi.get('name', 'Unknown')} ({poi.get('poi_type', 'Unknown type')})")
        
        return True
        
    except Exception as e:
        logger.error(f"POI去重测试失败: {str(e)}")
        return False

async def test_unified_event_bus():
    """测试统一事件总线"""
    logger.info("=== 测试统一事件总线 ===")
    
    try:
        from src.services.unified_event_bus import UnifiedEventBus
        from src.database.redis_client import RedisClient
        
        # 创建Redis客户端
        redis_client = RedisClient()
        event_bus = UnifiedEventBus(redis_client)
        
        task_id = f"test_task_{int(datetime.now().timestamp())}"
        
        # 测试事件发布
        await event_bus.notify_phase_start(
            task_id,
            "test_phase",
            "测试阶段",
            "正在测试事件总线功能..."
        )
        
        await event_bus.notify_planning_log(
            task_id,
            "这是一条测试规划日志",
            1
        )
        
        logger.info("事件总线测试成功")
        return True
        
    except Exception as e:
        logger.error(f"事件总线测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始旅行规划系统重构验证测试")
    
    test_results = {}
    
    # 执行各项测试
    test_results["poi_search"] = await test_poi_search_retry()
    test_results["icp_tools"] = await test_icp_tools()
    test_results["poi_deduplication"] = await test_poi_deduplication()
    test_results["event_bus"] = await test_unified_event_bus()
    
    # 汇总测试结果
    logger.info("\n=== 测试结果汇总 ===")
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    logger.info(f"\n总计: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！系统重构验证成功！")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
