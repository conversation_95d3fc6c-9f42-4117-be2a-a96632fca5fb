# 旅行规划中的时空连续性逻辑 (V3 - ICP架构版)

本文档详细说明了 AutoPilot AI 项目在V3统一架构下，如何通过**“迭代式上下文规划”（ICP）**模型，从根本上保证行程规划在**时间**和**空间**两个维度上的连续性与合理性。

## 1. 核心思想：从“静态排序”到“动态时序规划”

旧版架构采用的是一种**“静态地理排序”**的后处理方法，即先为一天分配完所有POI，再对它们进行一次性的经纬度排序。该方法虽简单，但无法真正理解时空关系，也无法处理复杂的场景。

**V3架构彻底抛弃了这种模式，转而采用一种更智能的、动态的“每日时序规划”逻辑。**

其核心思想是：**Agent的每一步思考，都是基于“当前在(where)”、“现在是(when)”这两个关键维度，来决策“下一步去(what)”。**

## 2. 时空连续性的实现机制

时空连续性不再是一个独立的“优化步骤”，而是**内嵌于`run_icp_planning`核心循环的内在属性**。它通过以下三个关键要素协同实现：

### 2.1. 实时状态管理 (Real-time State Management)
在`StandardAgentState`中，我们维护两个核心的动态变量，它们是实现时序规划的基石：
-   `current_location`: `str` - Agent当前所在的**空间位置**（通常是上一个POI的坐标或酒店坐标）。
-   `current_time`: `str` - Agent内部维护的**虚拟时钟**（例如 "09:00", "12:30"）。

### 2.2. 上下文感知的AI思考 (Context-Aware AI Thinking)
在ICP循环的每一步，Agent调用的`think_tool`都会接收到包含`current_location`和`current_time`的完整上下文。LLM的Prompt被明确设计为回答以下问题：
> "今天是第 {day} 天，现在是 {time}，我位于 {location}。根据用户偏好和行程逻辑（例如，中午要吃饭），我下一步最应该做什么？预估耗时多久？"

这种模式强迫AI在时空的约束下进行决策。

### 2.3. 原子化的时空工具 (Atomic Spatio-Temporal Tools)
为了支持AI的决策，工具集(`UnifiedToolRegistry`)提供了精确的、原子化的时空操作工具：
-   `search_nearby_poi(location, type, radius)`: **实现空间感知**。AI决策“下一步要去景点”后，会调用此工具在**当前位置附近**搜索，而不是全局搜索，从源头上保证了地理位置的连续性。
-   `get_driving_time(origin, destination)`: **实现时间推进**。在确定下一个POI后，调用此工具获取交通耗时。这个耗时会被用来精确地推进`current_time`虚拟时钟，确保行程时间的准确性。

## 3. 工作流示例

1.  **启动**: 规划开始，`current_location`设为酒店坐标，`current_time`设为`09:00`。
2.  **迭代1 (上午活动)**:
    -   **思考**: LLM接收到`(酒店, 09:00)`，决定“规划一个上午的景点，耗时3小时”。
    -   **行动**: Agent调用`search_nearby_poi(location=酒店坐标, type='景点')`，找到“南山广化寺”。
    -   **调度**: Agent调用`get_driving_time(酒店, 南山广化寺)`得到交通时间（如20分钟）。`current_time`推进为`09:20`。将“09:20-12:20 南山广化寺”加入行程。状态更新：`current_location`变为“南山广化寺”，`current_time`变为`12:20`。
3.  **迭代2 (午餐)**:
    -   **思考**: LLM接收到`(南山广化寺, 12:20)`，判断已到饭点，决定“规划午餐，耗时1.5小时”。
    -   **行动**: Agent调用`search_nearby_poi(location=南山广化寺坐标, type='餐厅')`，找到“附近的高分餐厅”。
    -   **调度**: ... 以此类推。

## 4. (已归档) 旧版地理排序逻辑

本节内容仅为历史存档，**已不再是项目当前使用的核心逻辑**。

### 逻辑概览
旧版方法在将POI分配到每一天后，通过`_optimize_daily_route`函数，对POI列表进行“先按经度、再按纬度”的简单排序。

### 局限性
-   **缺乏时间维度**: 无法判断活动安排在上午还是下午更合适。
-   **非最优路径**: 简单的坐标排序不等于最短旅行路径。
-   **场景适应性差**: 无法处理“先回酒店休整再进行夜间活动”等复杂场景。

## 5. 结论

在V3架构中，基于距离的规划不再是一个独立的、后置的优化步骤，而是通过**ICP循环、实时状态管理和原子化时空工具**的协同作用，成为整个智能规划流程中一个**原生、内置、贯穿始终**的核心能力。这种新范式从根本上保证了最终行程的时空合理性与连续性。
